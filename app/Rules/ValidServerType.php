<?php

namespace App\Rules;

use App\Enums\Subscriptions;
use App\Models\Subscription;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidServerType implements ValidationRule
{
    protected $subscriptionId;
    protected $licenseType;

    public function __construct($subscriptionId = null, $licenseType = null)
    {
        $this->subscriptionId = $subscriptionId;
        $this->licenseType = $licenseType;
    }

    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // If no subscription ID provided, allow all server types
        if (!$this->subscriptionId) {
            return;
        }

        // Get the subscription
        $subscription = Subscription::find($this->subscriptionId);
        if (!$subscription) {
            $fail('Invalid subscription.');
            return;
        }

        // Determine subscription type
        $subscriptionType = $subscription->isSaas() ? 'saas' : 'on-prem';
        
        // Use provided license type or determine from subscription
        $licenseType = $this->licenseType;
        if (!$licenseType) {
            $licenseType = $subscription->plan?->isFreeTrial() 
                ? Subscriptions::LICENSE_TYPE_EVALUATION->value 
                : Subscriptions::LICENSE_TYPE_COMMERCIAL->value;
        }

        // Get allowed server types
        $allowedServerTypes = Subscriptions::getAllowedServerTypes($subscriptionType, $licenseType);
        
        // Check if the provided server type is allowed
        if (!array_key_exists($value, $allowedServerTypes)) {
            $subscriptionTypeLabel = $subscriptionType === 'saas' ? 'SaaS' : 'On-Prem';
            $licenseTypeLabel = $licenseType === 'evaluation' ? 'Evaluation' : 'Commercial';
            
            $fail("The server type '{$value}' is not allowed for {$subscriptionTypeLabel} {$licenseTypeLabel} subscriptions. Allowed types: " . implode(', ', array_keys($allowedServerTypes)));
        }
    }
}
