<?php

namespace App\Enums;

use App\Traits\EnumLabels;

enum Subscriptions: string
{
    use EnumLabels;

        // PLANS
    case TRIAL_VERSION = 'trial-version';
    case ENTERPRISE_EDITION = 'enterprise-edition';
    case DATA_CENTER_EDITION = 'data-center-edition';

        // SUBSCRIPTION STATUSES
    case SUBSCRIPTION_STATUS_PENDING_PAYMENT = 'pending_payment';
    case SUBSCRIPTION_STATUS_PENDING_RECEIPT = 'pending_receipt';
    case SUBSCRIPTION_STATUS_PENDING_LICENSE_KEY = 'pending_license_key';
    case SUBSCRIPTION_STATUS_INCOMPLETE = 'incomplete';
    case SUBSCRIPTION_STATUS_ACTIVE = 'active';
    case SUBSCRIPTION_STATUS_EXPIRED = 'expired';
    case SUBSCRIPTION_STATUS_SUSPENDED = 'suspended';
    case SUBSCRIPTION_STATUS_TERMINATED = 'terminated';

        // SUBSCRIPTION PAYMENT STATUSES
    case SUBSCRIPTION_PAYMENT_STATUS_PENDING = 'pending';
    case SUBSCRIPTION_PAYMENT_STATUS_IN_REVIEW = 'in_review';
    case SUBSCRIPTION_PAYMENT_STATUS_FAILED = 'failed';
    case SUBSCRIPTION_PAYMENT_STATUS_PAID = 'paid';
    case SUBSCRIPTION_PAYMENT_STATUS_REVERSED = 'reversed';

        // PAYMENT TYPES
    case PAYMENT_TYPE_ONLINE_PAYMENT = 'online_payment';
    case PAYMENT_TYPE_WIRE_TRANSFER = 'wire_transfer';

        // LICENSE TYPES
    case LICENSE_TYPE_EVALUATION = 'evaluation';
    case LICENSE_TYPE_COMMERCIAL = 'commercial';

        // ENVIRONMENT OPTIONS
    case ENVIRONMENT_OPTION_PRODUCTION = 'production';
    case ENVIRONMENT_OPTION_DISASTER_RECOVERY = 'disaster_recovery';
    case ENVIRONMENT_OPTION_TEST = 'test';
    case ENVIRONMENT_OPTION_UAT = 'uat';

        // HISTORY EVENT TYPES
    case HISTORY_EVENT_TYPE_NEW = 'new';
    case HISTORY_EVENT_TYPE_RENEWAL = 'renewal';
    case HISTORY_EVENT_TYPE_SUSPENSION = 'suspension';
    case HISTORY_EVENT_TYPE_TERMINATION = 'termination';
    case HISTORY_EVENT_TYPE_ASSIGNED_QUOTA_CHANGE = 'assigned_quota_change';
    case HISTORY_EVENT_TYPE_REMAINING_QUOTA_CHANGE = 'remaining_quota_change';
    case HISTORY_EVENT_TYPE_PAYMENT_UPDATE = 'payment_update';
    case HISTORY_EVENT_TYPE_PAYMENT_APPROVED = 'payment_approved';
    case HISTORY_EVENT_TYPE_PAYMENT_REJECTED = 'payment_rejected';
    case HISTORY_EVENT_TYPE_LICENSE_KEY = 'license_key';
    case HISTORY_EVENT_TYPE_ACTIVATION = 'activation';
    case HISTORY_EVENT_TYPE_AUTO_RENEW_SETTING_CHANGE = 'auto_renew_setting_change';
    case HISTORY_EVENT_TYPE_EXTENSION = 'extension';
    case HISTORY_EVENT_TYPE_SUBSCRIPTION_UPDATE = 'subscription_update';

        // LICENSE HISTORY EVENT TYPES
    case LICENSE_HISTORY_CREATED = 'created';
    case LICENSE_HISTORY_UPDATED = 'updated';
    case LICENSE_HISTORY_REVOKED = 'revoked';
    case LICENSE_HISTORY_RENEWED = 'renewed';
    case LICENSE_HISTORY_SERVER_ID_ADDED = 'server_id_added';
    case LICENSE_HISTORY_SERVER_ID_MODIFIED = 'server_id_modified';
    case LICENSE_HISTORY_LICENSE_KEY_PROVIDED = 'license_key_provided';

        // SUBSCRIPTION LOG EVENTS
    case SUBSCRIPTION_LOG_EVENT_TYPE_NEW = 'new_subscription';

        // SUBSCRIPTION PROGRESS STEPS
    case STEP_SELECTED_PLAN = 'selected_plan';
    case STEP_PROVIDED_ADDRESS = 'provided_address';
    case STEP_ENTERED_PAYMENT_DETAILS = 'entered_payment_details';
    case STEP_CONFIRMED_ORDER = 'confirmed_order';

        // ENVIRONMENT STATUS
    case ENVIRONMENT_STATUS_ACTIVE = 'environment_active';
    case ENVIRONMENT_STATUS_PENDING = 'environment_pending';
    case ENVIRONMENT_STATUS_FAILED = 'environment_failed';


    /**
     * Get the label for the enum case.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            // PLANS
            self::TRIAL_VERSION => 'Trial Version',
            self::ENTERPRISE_EDITION => 'Enterprise Edition',
            self::DATA_CENTER_EDITION => 'Data Center Edition',

            // SUBSCRIPTION STATUSES
            self::SUBSCRIPTION_STATUS_PENDING_PAYMENT => 'Pending Payment',
            self::SUBSCRIPTION_STATUS_PENDING_RECEIPT => 'Pending Receipt',
            self::SUBSCRIPTION_STATUS_PENDING_LICENSE_KEY => 'Pending License Key',
            self::SUBSCRIPTION_STATUS_INCOMPLETE => 'New',
            self::SUBSCRIPTION_STATUS_ACTIVE => 'Active',
            self::SUBSCRIPTION_STATUS_EXPIRED => 'Expired',
            self::SUBSCRIPTION_STATUS_SUSPENDED => 'Suspended',
            self::SUBSCRIPTION_STATUS_TERMINATED => 'Terminated',

            // SUBSCRIPTION PAYMENT STATUSES
            self::SUBSCRIPTION_PAYMENT_STATUS_PENDING => 'Pending Payment',
            self::SUBSCRIPTION_PAYMENT_STATUS_IN_REVIEW => 'Payment Review',
            self::SUBSCRIPTION_PAYMENT_STATUS_FAILED => 'Failed',
            self::SUBSCRIPTION_PAYMENT_STATUS_PAID => 'Paid',
            self::SUBSCRIPTION_PAYMENT_STATUS_REVERSED => 'Reversed',

            // PAYMENT TYPES
            self::PAYMENT_TYPE_ONLINE_PAYMENT => 'Online Payment',
            self::PAYMENT_TYPE_WIRE_TRANSFER => 'Wire Transfer',

            // LICENSE TYPES
            self::LICENSE_TYPE_EVALUATION => 'Evaluation',
            self::LICENSE_TYPE_COMMERCIAL => 'Commercial',

            // ENVIRONMENT OPTIONS
            self::ENVIRONMENT_OPTION_PRODUCTION => 'Production',
            self::ENVIRONMENT_OPTION_DISASTER_RECOVERY => 'Disaster Recovery',
            self::ENVIRONMENT_OPTION_UAT => 'UAT',
            self::ENVIRONMENT_OPTION_TEST => 'Test',

            // ENVIRONMENT STATUS
            self::ENVIRONMENT_STATUS_ACTIVE => 'Active',
            self::ENVIRONMENT_STATUS_PENDING => 'Pending',
            self::ENVIRONMENT_STATUS_FAILED => 'Failed',

            // HISTORY EVENT TYPES
            self::HISTORY_EVENT_TYPE_NEW => 'New',
            self::HISTORY_EVENT_TYPE_RENEWAL => 'Renewal',
            self::HISTORY_EVENT_TYPE_SUSPENSION => 'Suspension',
            self::HISTORY_EVENT_TYPE_TERMINATION => 'Termination',
            self::HISTORY_EVENT_TYPE_ASSIGNED_QUOTA_CHANGE => 'Assigned Quota Change',
            self::HISTORY_EVENT_TYPE_REMAINING_QUOTA_CHANGE => 'Remaining Quota Change',
            self::HISTORY_EVENT_TYPE_PAYMENT_UPDATE => 'Payment Update', // todo: receipt uploaded
            self::HISTORY_EVENT_TYPE_LICENSE_KEY => 'License Key',
            self::HISTORY_EVENT_TYPE_PAYMENT_APPROVED => 'Payment approved',
            self::HISTORY_EVENT_TYPE_PAYMENT_REJECTED => 'Payment rejected',
            self::HISTORY_EVENT_TYPE_ACTIVATION => 'License Activated',
            self::HISTORY_EVENT_TYPE_AUTO_RENEW_SETTING_CHANGE => 'Auto Renew Setting Change',
            self::HISTORY_EVENT_TYPE_EXTENSION => 'Extension',
            self::HISTORY_EVENT_TYPE_SUBSCRIPTION_UPDATE => 'Subscription Update',

            // LICENSE HISTORY EVENT TYPES
            self::LICENSE_HISTORY_CREATED => 'Created',
            self::LICENSE_HISTORY_UPDATED => 'Updated',
            self::LICENSE_HISTORY_REVOKED => 'Revoked',
            self::LICENSE_HISTORY_RENEWED => 'Renewed',
            self::LICENSE_HISTORY_SERVER_ID_ADDED => 'Server ID Added',
            self::LICENSE_HISTORY_SERVER_ID_MODIFIED => 'Server ID Modified',
            self::LICENSE_HISTORY_LICENSE_KEY_PROVIDED => 'License Key Provided',

            // SUBSCRIPTION LOG EVENTS
            self::SUBSCRIPTION_LOG_EVENT_TYPE_NEW => 'Subscribe',

            // SUBSCRIPTION PROGRESS STEPS
            self::STEP_SELECTED_PLAN => 'Selected Plan',
            self::STEP_PROVIDED_ADDRESS => 'Provided Address',
            self::STEP_ENTERED_PAYMENT_DETAILS => 'Entered Payment Details',
            self::STEP_CONFIRMED_ORDER => 'Confirmed Order',

            default => $this->value,
        };
    }

    public function description()
    {
        return match ($this) {
            // HISTORY EVENT TYPES
            self::SUBSCRIPTION_LOG_EVENT_TYPE_NEW => 'Create New Subscription',
        };
    }

    /**
     * Get environment options
     */
    public static function getEnvironmentOptions(): array
    {
        return [
            self::ENVIRONMENT_OPTION_PRODUCTION->value => self::ENVIRONMENT_OPTION_PRODUCTION->label(),
            self::ENVIRONMENT_OPTION_DISASTER_RECOVERY->value => self::ENVIRONMENT_OPTION_DISASTER_RECOVERY->label(),
            self::ENVIRONMENT_OPTION_UAT->value => self::ENVIRONMENT_OPTION_UAT->label(),
            self::ENVIRONMENT_OPTION_TEST->value => self::ENVIRONMENT_OPTION_TEST->label(),
        ];
    }

    /**
     * Get subscription status options
     */
    public static function getSubscriptionStatusOptions(): array
    {
        return [
            self::SUBSCRIPTION_STATUS_PENDING_PAYMENT->value => self::SUBSCRIPTION_STATUS_PENDING_PAYMENT->label(),
            self::SUBSCRIPTION_STATUS_PENDING_RECEIPT->value => self::SUBSCRIPTION_STATUS_PENDING_RECEIPT->label(),
            self::SUBSCRIPTION_STATUS_PENDING_LICENSE_KEY->value => self::SUBSCRIPTION_STATUS_PENDING_LICENSE_KEY->label(),
            self::SUBSCRIPTION_STATUS_ACTIVE->value => self::SUBSCRIPTION_STATUS_ACTIVE->label(),
            self::SUBSCRIPTION_STATUS_EXPIRED->value => self::SUBSCRIPTION_STATUS_EXPIRED->label(),
            self::SUBSCRIPTION_STATUS_SUSPENDED->value => self::SUBSCRIPTION_STATUS_SUSPENDED->label(),
            self::SUBSCRIPTION_STATUS_TERMINATED->value => self::SUBSCRIPTION_STATUS_TERMINATED->label(),
        ];
    }

    /**
     * Get payment status options
     */
    public static function getPaymentStatusOptions(): array
    {
        return [
            self::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value => self::SUBSCRIPTION_PAYMENT_STATUS_PENDING->label(),
            self::SUBSCRIPTION_PAYMENT_STATUS_IN_REVIEW->value => self::SUBSCRIPTION_PAYMENT_STATUS_IN_REVIEW->label(),
            self::SUBSCRIPTION_PAYMENT_STATUS_FAILED->value => self::SUBSCRIPTION_PAYMENT_STATUS_FAILED->label(),
            self::SUBSCRIPTION_PAYMENT_STATUS_PAID->value => self::SUBSCRIPTION_PAYMENT_STATUS_PAID->label(),
            self::SUBSCRIPTION_PAYMENT_STATUS_REVERSED->value => self::SUBSCRIPTION_PAYMENT_STATUS_REVERSED->label(),
        ];
    }

    /**
     * Get license type options
     */
    public static function getLicenseTypeOptions(): array
    {
        return [
            self::LICENSE_TYPE_EVALUATION->value => self::LICENSE_TYPE_EVALUATION->label(),
            self::LICENSE_TYPE_COMMERCIAL->value => self::LICENSE_TYPE_COMMERCIAL->label(),
        ];
    }

    /**
     * Get Subscription Progress steps 
     */
    public static function getSubscriptionProgressSteps()
    {
        return [
            self::STEP_SELECTED_PLAN->value => self::STEP_SELECTED_PLAN->label(),
            self::STEP_PROVIDED_ADDRESS->value => self::STEP_PROVIDED_ADDRESS->label(),
            self::STEP_ENTERED_PAYMENT_DETAILS->value => self::STEP_ENTERED_PAYMENT_DETAILS->label(),
            self::STEP_CONFIRMED_ORDER->value => self::STEP_CONFIRMED_ORDER->label(),
        ];
    }
}
