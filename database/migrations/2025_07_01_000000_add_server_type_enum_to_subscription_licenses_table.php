<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add server_type enum column to subscription_licenses table
        Schema::table('subscription_licenses', function (Blueprint $table) {
            $table->enum('server_type', ['development', 'test', 'uat', 'production', 'disaster_recovery'])
                  ->nullable()
                  ->after('server_id')
                  ->comment('Type of server environment');
        });

        // Add server_type enum column to subscription_license_history table
        Schema::table('subscription_license_history', function (Blueprint $table) {
            $table->enum('server_type', ['development', 'test', 'uat', 'production', 'disaster_recovery'])
                  ->nullable()
                  ->after('server_id')
                  ->comment('Type of server environment');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_licenses', function (Blueprint $table) {
            $table->dropColumn('server_type');
        });

        Schema::table('subscription_license_history', function (Blueprint $table) {
            $table->dropColumn('server_type');
        });
    }
};
